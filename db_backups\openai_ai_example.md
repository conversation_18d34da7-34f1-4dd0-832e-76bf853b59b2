<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GPT-4o Chat App with File Upload</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .chat-container {
            width: 90%;
            max-width: 800px;
            height: 80vh;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        .chat-header {
            background: linear-gradient(135deg, #4a90e2, #357abd);
            color: white;
            padding: 20px;
            text-align: center;
            font-size: 1.2em;
            font-weight: 600;
        }

        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            background: #f8f9fa;
        }

        .message {
            margin-bottom: 20px;
            display: flex;
            gap: 10px;
        }

        .message.user {
            justify-content: flex-end;
        }

        .message-content {
            max-width: 70%;
            padding: 12px 16px;
            border-radius: 18px;
            word-wrap: break-word;
            line-height: 1.4;
        }

        .message.user .message-content {
            background: #4a90e2;
            color: white;
            border-bottom-right-radius: 4px;
        }

        .message.assistant .message-content {
            background: white;
            color: #333;
            border: 1px solid #e0e0e0;
            border-bottom-left-radius: 4px;
        }

        .file-attachment {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-top: 8px;
            padding: 8px 12px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            font-size: 14px;
        }

        .file-attachment.user {
            background: rgba(255, 255, 255, 0.2);
        }

        .file-attachment.assistant {
            background: #f0f0f0;
        }

        .file-icon {
            width: 16px;
            height: 16px;
            flex-shrink: 0;
        }

        .uploaded-image {
            max-width: 100%;
            max-height: 200px;
            border-radius: 8px;
            margin-top: 8px;
        }

        .chat-input-container {
            padding: 20px;
            background: white;
            border-top: 1px solid #e0e0e0;
        }

        .file-upload-area {
            margin-bottom: 15px;
            padding: 15px;
            border: 2px dashed #e0e0e0;
            border-radius: 12px;
            text-align: center;
            transition: all 0.3s;
            cursor: pointer;
        }

        .file-upload-area:hover {
            border-color: #4a90e2;
            background: #f8f9ff;
        }

        .file-upload-area.dragover {
            border-color: #4a90e2;
            background: #f0f8ff;
        }

        .file-upload-text {
            color: #666;
            font-size: 14px;
            margin-bottom: 8px;
        }

        .file-upload-button {
            background: #4a90e2;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 20px;
            cursor: pointer;
            font-size: 12px;
            transition: background 0.3s;
        }

        .file-upload-button:hover {
            background: #357abd;
        }

        .selected-files {
            margin-bottom: 15px;
        }

        .selected-file {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 8px 12px;
            background: #f0f8ff;
            border: 1px solid #4a90e2;
            border-radius: 8px;
            margin-bottom: 8px;
        }

        .file-info {
            flex: 1;
            font-size: 14px;
        }

        .file-name {
            font-weight: 500;
            color: #333;
        }

        .file-size {
            color: #666;
            font-size: 12px;
        }

        .remove-file {
            background: #ff6b6b;
            color: white;
            border: none;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            cursor: pointer;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .input-row {
            display: flex;
            gap: 10px;
        }

        .chat-input {
            flex: 1;
            padding: 12px 16px;
            border: 2px solid #e0e0e0;
            border-radius: 25px;
            font-size: 16px;
            outline: none;
            transition: border-color 0.3s;
        }

        .chat-input:focus {
            border-color: #4a90e2;
        }

        .send-button {
            padding: 12px 24px;
            background: #4a90e2;
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-weight: 600;
            transition: background 0.3s;
        }

        .send-button:hover:not(:disabled) {
            background: #357abd;
        }

        .send-button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }

        .typing-indicator {
            display: none;
            padding: 12px 16px;
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 18px;
            border-bottom-left-radius: 4px;
            max-width: 70%;
            margin-bottom: 20px;
        }

        .typing-dots {
            display: flex;
            gap: 4px;
        }

        .typing-dots span {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #999;
            animation: typing 1.4s infinite ease-in-out;
        }

        .typing-dots span:nth-child(1) { animation-delay: -0.32s; }
        .typing-dots span:nth-child(2) { animation-delay: -0.16s; }

        @keyframes typing {
            0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
            40% { transform: scale(1); opacity: 1; }
        }

        .error-message {
            background: #ff6b6b;
            color: white;
            padding: 10px;
            border-radius: 8px;
            margin-bottom: 10px;
            font-size: 14px;
        }

        .hidden {
            display: none !important;
        }

        @media (max-width: 600px) {
            .chat-container {
                width: 100%;
                height: 100vh;
                border-radius: 0;
            }
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <div class="chat-header">
            🤖 GPT-4o Chat Assistant with File Upload
        </div>
        
        <div class="chat-messages" id="chatMessages">
            <div class="message assistant">
                <div class="message-content">
                    Hello! I'm GPT-4o, your AI assistant. You can chat with me and also upload files (images, documents, PDFs, etc.) to discuss their content. How can I help you today?
                </div>
            </div>
        </div>
        
        <div class="typing-indicator" id="typingIndicator">
            <div class="typing-dots">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
        
        <div class="chat-input-container">
            <div class="file-upload-area" id="fileUploadArea">
                <div class="file-upload-text">Drop files here or click to upload</div>
                <button class="file-upload-button" onclick="document.getElementById('fileInput').click()">
                    📁 Choose Files
                </button>
                <input type="file" id="fileInput" multiple accept="*/*" class="hidden">
            </div>
            
            <div class="selected-files" id="selectedFiles"></div>
            
            <div class="input-row">
                <input 
                    type="text" 
                    id="chatInput" 
                    class="chat-input" 
                    placeholder="Type your message here..."
                    maxlength="500"
                >
                <button id="sendButton" class="send-button">Send</button>
            </div>
        </div>
    </div>

    <script>
        const API_KEY = '********************************************************';
        const API_URL = 'https://api.openai.com/v1/chat/completions';
        
        const chatMessages = document.getElementById('chatMessages');
        const chatInput = document.getElementById('chatInput');
        const sendButton = document.getElementById('sendButton');
        const typingIndicator = document.getElementById('typingIndicator');
        const fileInput = document.getElementById('fileInput');
        const fileUploadArea = document.getElementById('fileUploadArea');
        const selectedFilesDiv = document.getElementById('selectedFiles');
        
        let conversationHistory = [];
        let selectedFiles = [];

        // File upload handling
        fileInput.addEventListener('change', handleFileSelect);
        fileUploadArea.addEventListener('click', () => fileInput.click());
        fileUploadArea.addEventListener('dragover', handleDragOver);
        fileUploadArea.addEventListener('dragleave', handleDragLeave);
        fileUploadArea.addEventListener('drop', handleDrop);

        function handleFileSelect(e) {
            const files = Array.from(e.target.files);
            addFiles(files);
        }

        function handleDragOver(e) {
            e.preventDefault();
            fileUploadArea.classList.add('dragover');
        }

        function handleDragLeave(e) {
            e.preventDefault();
            fileUploadArea.classList.remove('dragover');
        }

        function handleDrop(e) {
            e.preventDefault();
            fileUploadArea.classList.remove('dragover');
            const files = Array.from(e.dataTransfer.files);
            addFiles(files);
        }

        function addFiles(files) {
            files.forEach(file => {
                if (file.size > 20 * 1024 * 1024) { // 20MB limit
                    showError(`File "${file.name}" is too large. Maximum size is 20MB.`);
                    return;
                }
                selectedFiles.push(file);
            });
            updateSelectedFilesDisplay();
        }

        function removeFile(index) {
            selectedFiles.splice(index, 1);
            updateSelectedFilesDisplay();
        }

        function updateSelectedFilesDisplay() {
            if (selectedFiles.length === 0) {
                selectedFilesDiv.innerHTML = '';
                return;
            }

            selectedFilesDiv.innerHTML = selectedFiles.map((file, index) => `
                <div class="selected-file">
                    <div class="file-info">
                        <div class="file-name">📄 ${file.name}</div>
                        <div class="file-size">${formatFileSize(file.size)}</div>
                    </div>
                    <button class="remove-file" onclick="removeFile(${index})">×</button>
                </div>
            `).join('');
        }

        function formatFileSize(bytes) {
            if (bytes < 1024) return bytes + ' bytes';
            if (bytes < 1024 * 1024) return Math.round(bytes / 1024) + ' KB';
            return Math.round(bytes / (1024 * 1024)) + ' MB';
        }

        async function fileToBase64(file) {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.onload = () => resolve(reader.result.split(',')[1]);
                reader.onerror = reject;
                reader.readAsDataURL(file);
            });
        }

        async function readTextFile(file) {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.onload = () => resolve(reader.result);
                reader.onerror = reject;
                reader.readAsText(file);
            });
        }

        function getFileIcon(fileName) {
            const ext = fileName.split('.').pop().toLowerCase();
            const icons = {
                'pdf': '📋',
                'doc': '📄', 'docx': '📄',
                'txt': '📝',
                'jpg': '🖼️', 'jpeg': '🖼️', 'png': '🖼️', 'gif': '🖼️',
                'mp4': '🎥', 'avi': '🎥', 'mov': '🎥',
                'mp3': '🎵', 'wav': '🎵',
                'zip': '📦', 'rar': '📦',
                'js': '💻', 'html': '💻', 'css': '💻', 'py': '💻'
            };
            return icons[ext] || '📄';
        }

        function isImageFile(fileName) {
            const ext = fileName.split('.').pop().toLowerCase();
            return ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp'].includes(ext);
        }

        function isTextFile(fileName) {
            const ext = fileName.split('.').pop().toLowerCase();
            return ['txt', 'js', 'html', 'css', 'py', 'md', 'json', 'xml', 'csv'].includes(ext);
        }

        function addMessage(content, isUser = false, files = []) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${isUser ? 'user' : 'assistant'}`;
            
            const messageContent = document.createElement('div');
            messageContent.className = 'message-content';
            messageContent.textContent = content;
            
            messageDiv.appendChild(messageContent);

            // Add file attachments
            files.forEach(file => {
                if (isImageFile(file.name)) {
                    const img = document.createElement('img');
                    img.className = 'uploaded-image';
                    img.src = URL.createObjectURL(file);
                    messageContent.appendChild(img);
                } else {
                    const fileDiv = document.createElement('div');
                    fileDiv.className = `file-attachment ${isUser ? 'user' : 'assistant'}`;
                    fileDiv.innerHTML = `
                        <span class="file-icon">${getFileIcon(file.name)}</span>
                        <span>${file.name}</span>
                        <span style="font-size: 12px; opacity: 0.7;">(${formatFileSize(file.size)})</span>
                    `;
                    messageContent.appendChild(fileDiv);
                }
            });
            
            chatMessages.appendChild(messageDiv);
            
            // Auto-scroll to bottom
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        function showTypingIndicator() {
            typingIndicator.style.display = 'block';
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        function hideTypingIndicator() {
            typingIndicator.style.display = 'none';
        }

        function showError(message) {
            const errorDiv = document.createElement('div');
            errorDiv.className = 'error-message';
            errorDiv.textContent = `Error: ${message}`;
            chatMessages.appendChild(errorDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        async function sendMessage() {
            const message = chatInput.value.trim();
            const hasFiles = selectedFiles.length > 0;
            
            if (!message && !hasFiles) return;

            // Add user message to chat
            addMessage(message || "Uploaded files", true, selectedFiles);
            
            // Prepare message content
            let messageContent = [];
            
            if (message) {
                messageContent.push({
                    type: "text",
                    text: message
                });
            }

            // Process files
            const processedFiles = [];
            for (const file of selectedFiles) {
                try {
                    if (isImageFile(file.name)) {
                        const base64 = await fileToBase64(file);
                        messageContent.push({
                            type: "image_url",
                            image_url: {
                                url: `data:${file.type};base64,${base64}`
                            }
                        });
                    } else if (isTextFile(file.name)) {
                        const textContent = await readTextFile(file);
                        messageContent.push({
                            type: "text",
                            text: `File: ${file.name}\nContent:\n${textContent}`
                        });
                    } else {
                        // For other file types, just mention them
                        messageContent.push({
                            type: "text",
                            text: `File uploaded: ${file.name} (${formatFileSize(file.size)})`
                        });
                    }
                    processedFiles.push(file);
                } catch (error) {
                    console.error(`Error processing file ${file.name}:`, error);
                    showError(`Failed to process file: ${file.name}`);
                }
            }

            // Add to conversation history
            conversationHistory.push({
                role: 'user',
                content: messageContent.length === 1 ? messageContent[0].text : messageContent
            });

            // Clear inputs
            chatInput.value = '';
            selectedFiles = [];
            updateSelectedFilesDisplay();
            
            // Disable input while processing
            sendButton.disabled = true;
            chatInput.disabled = true;
            showTypingIndicator();

            try {
                const response = await fetch(API_URL, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${API_KEY}`
                    },
                    body: JSON.stringify({
                        model: 'gpt-4o',
                        messages: [
                            {
                                role: 'system',
                                content: 'You are a helpful assistant that can analyze files, images, and documents. When users upload files, help them understand and work with the content.'
                            },
                            ...conversationHistory
                        ],
                        max_tokens: 1500,
                        temperature: 0.7
                    })
                });

                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.error?.message || `HTTP ${response.status}`);
                }

                const data = await response.json();
                const assistantMessage = data.choices[0].message.content;
                
                hideTypingIndicator();
                addMessage(assistantMessage);

                // Add assistant response to history
                conversationHistory.push({
                    role: 'assistant',
                    content: assistantMessage
                });

            } catch (error) {
                hideTypingIndicator();
                console.error('Error:', error);
                showError(error.message || 'Failed to send message. Please try again.');
            } finally {
                // Re-enable input
                sendButton.disabled = false;
                chatInput.disabled = false;
                chatInput.focus();
            }
        }

        // Event listeners
        sendButton.addEventListener('click', sendMessage);
        
        chatInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });

        // Focus input on load
        chatInput.focus();

        // Keep conversation history reasonable (last 20 exchanges)
        setInterval(() => {
            if (conversationHistory.length > 40) {
                conversationHistory = conversationHistory.slice(-40);
            }
        }, 60000);
    </script>
</body>
</html>