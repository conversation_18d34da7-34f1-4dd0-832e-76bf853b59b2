<?= $this->extend('templates/home_template') ?>

<?= $this->section('css') ?>
<style>
  /* Jobs page specific styles */
  .job-card {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    transition: all 0.3s ease;
    height: 100%;
  }

  .job-card:hover {
    border-color: var(--red);
    box-shadow: 0 4px 12px rgba(240, 15, 0, 0.1);
    transform: translateY(-2px);
  }

  .position-title {
    color: var(--red);
    font-weight: 600;
    text-decoration: none;
  }

  .position-title:hover {
    color: var(--red);
    text-decoration: underline;
  }

  .view-toggle {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 4px;
  }

  .view-toggle .btn {
    border: none;
    background: transparent;
    color: #6c757d;
    padding: 8px 16px;
    border-radius: 6px;
    transition: all 0.3s ease;
  }

  .view-toggle .btn.active {
    background: white;
    color: var(--red);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  }

  .grid-view, .table-view {
    display: none;
  }

  .grid-view.active, .table-view.active {
    display: block;
  }

  .positions-table th {
    background-color: #f8f9fa;
    border-bottom: 2px solid var(--red);
    color: var(--red);
    font-weight: 600;
  }

  .badge {
    font-size: 0.75rem;
  }

  .bg-yellow {
    background-color: #ffc107 !important;
    color: #000;
  }

  .text-red {
    color: var(--red) !important;
  }

  .btn-red {
    background-color: var(--red);
    border-color: var(--red);
    color: white;
  }

  .btn-red:hover {
    background-color: #d60000;
    border-color: #d60000;
    color: white;
  }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
  <!-- Hero Section -->
  <section class="py-5 bg-light">
    <div class="container">
      <div class="row align-items-center">
        <div class="col-lg-8">
          <h1 class="display-5 fw-bold mb-3">Available Government Positions</h1>
          <p class="lead text-muted">Browse through current job openings across various government departments and agencies.</p>
        </div>
        <div class="col-lg-4 text-lg-end">
          <div class="d-flex align-items-center justify-content-lg-end">
            <span class="me-3 text-muted">Total Positions:</span>
            <span class="h4 mb-0 text-red fw-bold" id="totalPositionsCount"><?= count($positions) ?></span>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Filters Section -->
  <section class="py-4 bg-white border-bottom">
    <div class="container">
      <form method="GET" action="<?= base_url('jobs') ?>" class="row g-3">
        <div class="col-md-3">
          <label for="org_id" class="form-label">Organization</label>
          <select name="org_id" id="org_id" class="form-select">
            <option value="">All Organizations</option>
            <?php foreach ($organizations as $org): ?>
              <option value="<?= $org['id'] ?>" <?= $selectedOrgId == $org['id'] ? 'selected' : '' ?>>
                <?= esc($org['org_name']) ?>
              </option>
            <?php endforeach; ?>
          </select>
        </div>
        <div class="col-md-3">
          <label for="classification" class="form-label">Classification</label>
          <select name="classification" id="classification" class="form-select">
            <option value="">All Classifications</option>
            <?php foreach ($classifications as $classification): ?>
              <option value="<?= esc($classification) ?>" <?= $selectedClassification == $classification ? 'selected' : '' ?>>
                <?= esc($classification) ?>
              </option>
            <?php endforeach; ?>
          </select>
        </div>
        <div class="col-md-3">
          <label for="position_group" class="form-label">Position Group</label>
          <select name="position_group" id="position_group" class="form-select">
            <option value="">All Position Groups</option>
            <?php foreach ($positionGroups as $group): ?>
              <option value="<?= $group['id'] ?>" <?= $selectedPositionGroup == $group['id'] ? 'selected' : '' ?>>
                <?= esc($group['group_name']) ?>
              </option>
            <?php endforeach; ?>
          </select>
        </div>
        <div class="col-md-3">
          <label for="position_type" class="form-label">Type</label>
          <select name="position_type" id="position_type" class="form-select">
            <option value="">All Types</option>
            <option value="internal" <?= $selectedPositionType == 'internal' ? 'selected' : '' ?>>Internal</option>
            <option value="external" <?= $selectedPositionType == 'external' ? 'selected' : '' ?>>External</option>
          </select>
        </div>
        <div class="col-md-12">
          <label for="search" class="form-label">Search</label>
          <div class="input-group">
            <input type="text" name="search" id="search" class="form-control"
                   placeholder="Search positions..." value="<?= esc($searchTerm) ?>">
            <button type="submit" class="btn btn-red">
              <i class="fas fa-search me-2"></i>Filter/Search
            </button>
          </div>
        </div>
      </form>
    </div>
  </section>

  <!-- Positions Section -->
  <section class="py-5">
    <div class="container">
      <?php if (isset($error)): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
          <?= esc($error) ?>
          <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
      <?php endif; ?>

      <?php if (!empty($positions)): ?>
        <!-- View Toggle -->
        <div class="d-flex justify-content-between align-items-center mb-4">
          <h2 class="mb-0">Government Positions (<?= count($positions) ?>)</h2>
          <div class="view-toggle">
            <button type="button" class="btn active" id="gridViewBtn" onclick="toggleView('grid')">
              <i class="fas fa-th me-2"></i>Grid
            </button>
            <button type="button" class="btn" id="tableViewBtn" onclick="toggleView('table')">
              <i class="fas fa-list me-2"></i>Table
            </button>
          </div>
        </div>

        <!-- Grid View -->
        <div class="grid-view active" id="gridView">
          <div class="row g-4">
            <?php foreach ($positions as $position): ?>
              <div class="col-lg-4 col-md-6">
                <div class="card job-card h-100">
                  <div class="card-body p-4">
                    <h5 class="position-title mb-3">
                      <a href="<?= base_url('jobs/view/' . ($position['id'] ?? '1')) ?>" class="text-decoration-none">
                        <?= esc($position['designation'] ?? 'Position Not Specified') ?>
                      </a>
                    </h5>
                    <div class="position-meta mb-3">
                      <div class="mb-2">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-building me-2" viewBox="0 0 16 16">
                          <path d="M4 2.5a.5.5 0 0 1 .5-.5h1a.5.5 0 0 1 .5.5v1a.5.5 0 0 1-.5.5h-1a.5.5 0 0 1-.5-.5v-1Zm3 0a.5.5 0 0 1 .5-.5h1a.5.5 0 0 1 .5.5v1a.5.5 0 0 1-.5.5h-1a.5.5 0 0 1-.5-.5v-1Zm3.5-.5a.5.5 0 0 0-.5.5v1a.5.5 0 0 0 .5.5h1a.5.5 0 0 0 .5-.5v-1a.5.5 0 0 0-.5-.5h-1ZM4 5.5a.5.5 0 0 1 .5-.5h1a.5.5 0 0 1 .5.5v1a.5.5 0 0 1-.5.5h-1a.5.5 0 0 1-.5-.5v-1ZM7.5 5a.5.5 0 0 0-.5.5v1a.5.5 0 0 0 .5.5h1a.5.5 0 0 0 .5-.5v-1a.5.5 0 0 0-.5-.5h-1Zm2.5.5a.5.5 0 0 1 .5-.5h1a.5.5 0 0 1 .5.5v1a.5.5 0 0 1-.5.5h-1a.5.5 0 0 1-.5-.5v-1ZM4.5 8a.5.5 0 0 0-.5.5v1a.5.5 0 0 0 .5.5h1a.5.5 0 0 0 .5-.5v-1a.5.5 0 0 0-.5-.5h-1Zm2.5.5a.5.5 0 0 1 .5-.5h1a.5.5 0 0 1 .5.5v1a.5.5 0 0 1-.5.5h-1a.5.5 0 0 1-.5-.5v-1Zm3.5-.5a.5.5 0 0 0-.5.5v1a.5.5 0 0 0 .5.5h1a.5.5 0 0 0 .5-.5v-1a.5.5 0 0 0-.5-.5h-1Z"/>
                          <path d="M2 1a1 1 0 0 1 1-1h10a1 1 0 0 1 1 1v14a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V1Zm11 0H3v14h3v-2.5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5V15h3V1Z"/>
                        </svg>
                        <?= esc($position['org_name'] ?? 'Organization Not Specified') ?>
                      </div>
                      <div class="mb-2">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-collection me-2" viewBox="0 0 16 16">
                          <path d="M2.5 3.5a.5.5 0 0 1 0-1h11a.5.5 0 0 1 0 1h-11zm2-2a.5.5 0 0 1 0-1h7a.5.5 0 0 1 0 1h-7zM0 13a1.5 1.5 0 0 0 1.5 1.5h13A1.5 1.5 0 0 0 16 13V6a1.5 1.5 0 0 0-1.5-1.5h-13A1.5 1.5 0 0 0 0 6v7zm1.5.5A.5.5 0 0 1 1 13V6a.5.5 0 0 1 .5-.5h13a.5.5 0 0 1 .5.5v7a.5.5 0 0 1-.5.5h-13z"/>
                        </svg>
                        <?= esc($position['group_name'] ?? 'Position Group Not Specified') ?>
                      </div>
                      <div class="mb-2">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-geo-alt me-2" viewBox="0 0 16 16">
                          <path d="M12.166 8.94c-.524 1.062-1.234 2.12-1.96 3.07A31.493 31.493 0 0 1 8 14.58a31.481 31.481 0 0 1-2.206-2.57c-.726-.95-1.436-2.008-1.96-3.07C3.304 7.867 3 6.862 3 6a5 5 0 0 1 10 0c0 .862-.305 1.867-.834 2.94zM8 16s6-5.686 6-10A6 6 0 0 0 2 6c0 4.314 6 10 6 10z"/>
                          <path d="M8 8a2 2 0 1 1 0-4 2 2 0 0 1 0 4zm0 1a3 3 0 1 0 0-6 3 3 0 0 0 0 6z"/>
                        </svg>
                        <?= esc($position['location'] ?? 'Location Not Specified') ?>
                      </div>
                      <div class="mb-2">
                        <span class="badge bg-yellow me-2"><?= esc($position['classification'] ?? 'Not Specified') ?></span>
                        <?php if (isset($position['is_internal'])): ?>
                          <?php if ($position['is_internal'] == 1): ?>
                            <span class="badge bg-warning text-dark">Internal</span>
                          <?php else: ?>
                            <span class="badge bg-primary">External</span>
                          <?php endif; ?>
                        <?php endif; ?>
                      </div>
                      <div>
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-cash me-2" viewBox="0 0 16 16">
                          <path d="M8 10a2 2 0 1 0 0-4 2 2 0 0 0 0 4z"/>
                          <path d="M0 4a1 1 0 0 1 1-1h14a1 1 0 0 1 1 1v8a1 1 0 0 1-1 1H1a1 1 0 0 1-1-1V4zm3 0a2 2 0 0 1-2 2v4a2 2 0 0 1 2 2h10a2 2 0 0 1 2-2V6a2 2 0 0 1-2-2H3z"/>
                        </svg>
                        K<?= esc($position['annual_salary'] ?? '0') ?>
                      </div>
                    </div>
                    <div class="position-deadline mb-3">
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-calendar-event me-2" viewBox="0 0 16 16">
                        <path d="M11 6.5a.5.5 0 0 1 .5-.5h1a.5.5 0 0 1 .5.5v1a.5.5 0 0 1-.5.5h-1a.5.5 0 0 1-.5-.5v-1z"/>
                        <path d="M3.5 0a.5.5 0 0 1 .5.5V1h8V.5a.5.5 0 0 1 1 0V1h1a2 2 0 0 1 2 2v11a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V3a2 2 0 0 1 2-2h1V.5a.5.5 0 0 1 .5-.5zM1 4v10a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1V4H1z"/>
                      </svg>
                      Closes on <?= isset($position['publish_date_to']) ? date('d M Y', strtotime($position['publish_date_to'])) : 'Date Not Specified' ?>
                    </div>
                    <a href="<?= base_url('jobs/view/' . ($position['id'] ?? '1')) ?>" class="btn btn-red w-100">View Details</a>
                  </div>
                </div>
              </div>
            <?php endforeach; ?>
          </div>
        </div>

        <!-- Table View -->
        <div class="table-view" id="tableView">
          <div class="table-responsive positions-table">
            <table class="table table-hover mb-0">
              <thead>
                <tr>
                  <th>Position</th>
                  <th>Organization</th>
                  <th>Position Group</th>
                  <th>Location</th>
                  <th>Classification</th>
                  <th>Type</th>
                  <th>Salary Range</th>
                  <th>Deadline</th>
                  <th>Action</th>
                </tr>
              </thead>
              <tbody>
                <?php foreach ($positions as $position): ?>
                  <tr>
                    <td>
                      <div class="fw-bold text-red"><?= esc($position['designation'] ?? 'Position Not Specified') ?></div>
                      <small class="text-muted"><?= esc($position['advertisement_no'] ?? 'N/A') ?></small>
                    </td>
                    <td><?= esc($position['org_name'] ?? 'Organization Not Specified') ?></td>
                    <td><?= esc($position['group_name'] ?? 'Position Group Not Specified') ?></td>
                    <td><?= esc($position['location'] ?? 'Location Not Specified') ?></td>
                    <td><span class="badge bg-yellow"><?= esc($position['classification'] ?? 'Not Specified') ?></span></td>
                    <td>
                      <?php if (isset($position['is_internal'])): ?>
                        <?php if ($position['is_internal'] == 1): ?>
                          <span class="badge bg-warning text-dark">Internal</span>
                        <?php else: ?>
                          <span class="badge bg-primary">External</span>
                        <?php endif; ?>
                      <?php else: ?>
                        <span class="badge bg-secondary">N/A</span>
                      <?php endif; ?>
                    </td>
                    <td>K<?= esc($position['annual_salary'] ?? '0') ?></td>
                    <td><?= isset($position['publish_date_to']) ? date('d M Y', strtotime($position['publish_date_to'])) : 'Date Not Specified' ?></td>
                    <td>
                      <a href="<?= base_url('jobs/view/' . ($position['id'] ?? '1')) ?>" class="btn btn-sm btn-red">View Details</a>
                    </td>
                  </tr>
                <?php endforeach; ?>
              </tbody>
            </table>
          </div>
        </div>

        <!-- Load More Button -->
        <div class="text-center mt-4" id="loadMoreSection">
          <button type="button" class="btn btn-red" id="loadMoreBtn">
            <i class="fas fa-plus me-2"></i>Load More Positions
          </button>
          <div class="mt-3" id="loadingSpinner" style="display: none;">
            <div class="spinner-border text-danger" role="status">
              <span class="visually-hidden">Loading...</span>
            </div>
            <p class="text-muted mt-2">Loading more positions...</p>
          </div>
        </div>

      <?php else: ?>
        <div class="text-center py-5">
          <svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" fill="currentColor" class="bi bi-clipboard-x mb-3 text-muted" viewBox="0 0 16 16">
            <path d="M6.5 0A1.5 1.5 0 0 0 5 1.5v1A1.5 1.5 0 0 0 6.5 4h3A1.5 1.5 0 0 0 11 2.5v-1A1.5 1.5 0 0 0 9.5 0zm3 1a.5.5 0 0 1 .5.5v1a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5v-1a.5.5 0 0 1 .5-.5z"/>
            <path d="M4 1.5H3a2 2 0 0 0-2 2V14a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2V3.5a2 2 0 0 0-2-2h-1v1A2.5 2.5 0 0 1 9.5 5h-3A2.5 2.5 0 0 1 4 2.5zm4 7.793 1.146-1.147a.5.5 0 1 1 .708.708L8.707 10l1.147 1.146a.5.5 0 0 1-.708.708L8 10.707l-1.146 1.147a.5.5 0 0 1-.708-.708L7.293 10 6.146 8.854a.5.5 0 1 1 .708-.708z"/>
          </svg>
          <h3 class="text-red">No Positions Available</h3>
          <p class="text-muted">Check back later for new government positions or adjust your filters.</p>
          <a href="<?= base_url('jobs') ?>" class="btn btn-red">Clear Filters</a>
        </div>
      <?php endif; ?>
    </div>
  </section>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
// Lazy loading variables
let currentPage = 1;
let isLoading = false;
let hasMoreData = true;
let totalPositionsLoaded = <?= count($positions) ?>;

function toggleView(viewType) {
    const gridView = document.getElementById('gridView');
    const tableView = document.getElementById('tableView');
    const gridBtn = document.getElementById('gridViewBtn');
    const tableBtn = document.getElementById('tableViewBtn');

    if (viewType === 'grid') {
        // Show grid view
        gridView.classList.add('active');
        tableView.classList.remove('active');

        // Update button states
        gridBtn.classList.add('active');
        tableBtn.classList.remove('active');
    } else if (viewType === 'table') {
        // Show table view
        tableView.classList.add('active');
        gridView.classList.remove('active');

        // Update button states
        tableBtn.classList.add('active');
        gridBtn.classList.remove('active');
    }
}

// Load more positions function
function loadMorePositions() {
    if (isLoading || !hasMoreData) return;

    isLoading = true;
    currentPage++;

    // Show loading spinner
    const loadingSpinner = document.getElementById('loadingSpinner');
    const loadMoreBtn = document.getElementById('loadMoreBtn');

    if (loadingSpinner) loadingSpinner.style.display = 'block';
    if (loadMoreBtn) loadMoreBtn.style.display = 'none';

    // Get current filter values
    const formData = new FormData(document.querySelector('form'));
    formData.append('page', currentPage);
    formData.append('ajax', '1');

    // Convert FormData to URLSearchParams
    const params = new URLSearchParams();
    for (let [key, value] of formData) {
        params.append(key, value);
    }

    fetch('<?= base_url('jobs') ?>?' + params.toString(), {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success && data.positions.length > 0) {
            // Append new positions to grid view
            const gridContainer = document.querySelector('#gridView .row');
            if (gridContainer) {
                gridContainer.insertAdjacentHTML('beforeend', data.gridHtml);
            }

            // Append new positions to table view
            const tableBody = document.querySelector('#tableView tbody');
            if (tableBody) {
                tableBody.insertAdjacentHTML('beforeend', data.tableHtml);
            }

            // Update total positions counter
            totalPositionsLoaded += data.positions.length;
            const totalCounter = document.getElementById('totalPositionsCount');
            if (totalCounter) {
                totalCounter.textContent = totalPositionsLoaded;
            }

            // Check if there are more positions to load
            if (data.positions.length < 50) {
                hasMoreData = false;
                const loadMoreSection = document.getElementById('loadMoreSection');
                if (loadMoreSection) {
                    loadMoreSection.innerHTML = '<p class="text-muted">No more positions to load.</p>';
                }
            } else {
                // Show load more button again
                const loadMoreBtn = document.getElementById('loadMoreBtn');
                if (loadMoreBtn) {
                    loadMoreBtn.style.display = 'block';
                }
            }
        } else {
            hasMoreData = false;
            const loadMoreSection = document.getElementById('loadMoreSection');
            if (loadMoreSection) {
                loadMoreSection.innerHTML = '<p class="text-muted">No more positions to load.</p>';
            }
        }
    })
    .catch(error => {
        console.error('Error loading more positions:', error);
        const loadMoreSection = document.getElementById('loadMoreSection');
        if (loadMoreSection) {
            loadMoreSection.innerHTML = '<p class="text-danger">Error loading more positions. Please try again.</p>';
        }
    })
    .finally(() => {
        isLoading = false;
        const loadingSpinner = document.getElementById('loadingSpinner');
        if (loadingSpinner) {
            loadingSpinner.style.display = 'none';
        }
    });
}

// Make function globally accessible
window.loadMorePositions = loadMorePositions;

// Scroll detection for automatic loading
function checkScrollPosition() {
    const scrollPosition = window.innerHeight + window.scrollY;
    const documentHeight = document.documentElement.offsetHeight;

    // Load more when user is 200px from bottom
    if (scrollPosition >= documentHeight - 200 && !isLoading && hasMoreData) {
        loadMorePositions();
    }
}

// Auto-submit form when filters change
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form');
    const selects = form.querySelectorAll('select');

    selects.forEach(select => {
        select.addEventListener('change', function() {
            // Reset lazy loading state when filters change
            currentPage = 1;
            hasMoreData = true;
            totalPositionsLoaded = 0; // Reset counter
            form.submit();
        });
    });

    // Add click event for load more button
    const loadMoreBtn = document.getElementById('loadMoreBtn');
    if (loadMoreBtn) {
        loadMoreBtn.addEventListener('click', function() {
            loadMorePositions();
        });
    }

    // Add scroll listener for lazy loading
    window.addEventListener('scroll', checkScrollPosition);
});
</script>
<?= $this->endSection() ?>