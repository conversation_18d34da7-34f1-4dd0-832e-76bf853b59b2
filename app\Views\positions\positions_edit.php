<?= $this->extend('templates/nolstemp') ?>
<?= $this->section('content') ?>
<div class="container mt-4">
    <div class="card">
        <div class="card-header bg-white py-3">
            <h4 class="mb-0">Edit Position</h4>
        </div>
        <form action="<?= base_url('positions/update') ?>" method="post" enctype="multipart/form-data">
            <?= csrf_field() ?>
            <input type="hidden" name="id" value="<?= esc($position['id']) ?>">
            <input type="hidden" name="position_group_id" value="<?= esc($position['position_group_id']) ?>">
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-md-6">
                        <label class="form-label">Position Reference</label>
                        <input type="text" class="form-control" name="position_reference" value="<?= esc($position['position_reference']) ?>" required>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">Designation</label>
                        <input type="text" class="form-control" name="designation" value="<?= esc($position['designation']) ?>" required>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">Classification</label>
                        <input type="text" class="form-control" name="classification" value="<?= esc($position['classification']) ?>">
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">Award</label>
                        <input type="text" class="form-control" name="award" value="<?= esc($position['award']) ?>">
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">Location</label>
                        <input type="text" class="form-control" name="location" value="<?= esc($position['location']) ?>">
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">Annual Salary</label>
                        <input type="text" class="form-control" name="annual_salary" value="<?= esc($position['annual_salary']) ?>">
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">Status</label>
                        <select class="form-select" name="status" required>
                            <option value="active" <?= $position['status'] == 'active' ? 'selected' : '' ?>>Active</option>
                            <option value="inactive" <?= $position['status'] == 'inactive' ? 'selected' : '' ?>>Inactive</option>
                        </select>
                    </div>
                    <div class="col-12">
                        <label class="form-label">Qualifications</label>
                        <textarea class="form-control" name="qualifications" rows="3"><?= esc($position['qualifications']) ?></textarea>
                    </div>
                    <div class="col-12">
                        <label class="form-label">Knowledge</label>
                        <textarea class="form-control" name="knowledge" rows="3"><?= esc($position['knowledge']) ?></textarea>
                    </div>
                    <div class="col-12">
                        <label class="form-label">Skills & Competencies</label>
                        <textarea class="form-control" name="skills_competencies" rows="3"><?= esc($position['skills_competencies']) ?></textarea>
                    </div>
                    <div class="col-12">
                        <label class="form-label">Job Experiences</label>
                        <textarea class="form-control" name="job_experiences" rows="3"><?= esc($position['job_experiences']) ?></textarea>
                    </div>
                    <div class="col-12">
                        <label class="form-label">Remarks</label>
                        <textarea class="form-control" name="remarks" rows="2"><?= esc($position['remarks']) ?></textarea>
                    </div>
                    <div class="col-12">
                        <label class="form-label">Job Description File (PDF only)</label>
                        <input type="file" class="form-control" name="jd_file" id="jdFileInput" accept=".pdf">
                        <?php if (!empty($position['jd_filepath'])): ?>
                        <small class="text-muted">Current file: <a href="<?= base_url($position['jd_filepath']) ?>" target="_blank">Download</a></small>
                        <?php endif; ?>

                        <!-- AI Processing Section -->
                        <div class="mt-3" id="aiProcessingSection" style="display: none;">
                            <div class="card border-primary">
                                <div class="card-header bg-primary text-white">
                                    <h6 class="mb-0">
                                        <i class="fas fa-robot me-2"></i>AI Document Content Analysis and Processing
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <!-- File Information -->
                                    <div class="file-info mb-3" id="fileInfo" style="display: none;">
                                        <div class="row g-3">
                                            <div class="col-md-6">
                                                <div class="info-item">
                                                    <label class="info-label">File Name:</label>
                                                    <span class="info-value" id="fileName"></span>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="info-item">
                                                    <label class="info-label">File Size:</label>
                                                    <span class="info-value" id="fileSize"></span>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="info-item">
                                                    <label class="info-label">Total Pages:</label>
                                                    <span class="info-value" id="totalPages"></span>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="info-item">
                                                    <label class="info-label">Processing Status:</label>
                                                    <span class="info-value" id="processingStatus">Ready</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Progress Section -->
                                    <div class="progress-section" id="progressSection" style="display: none;">
                                        <div class="status-text mb-2" id="statusText">Processing...</div>
                                        <div class="progress mb-2">
                                            <div class="progress-bar" id="progressFill" style="width: 0%"></div>
                                        </div>
                                        <div class="timing-info" id="timingInfo">
                                            <small class="text-muted">
                                                <strong>Status:</strong> <span id="currentStatus">Starting...</span>
                                            </small>
                                        </div>
                                    </div>

                                    <!-- Extracted Text Display -->
                                    <div class="extracted-text-section" id="extractedTextSection" style="display: none;">
                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <h6 class="mb-0">
                                                <i class="fas fa-file-alt me-2"></i>Processed Text (JSON Format)
                                            </h6>
                                            <button class="btn btn-sm btn-outline-secondary" type="button" data-bs-toggle="collapse" data-bs-target="#extractedTextCollapse">
                                                <i class="fas fa-eye me-1"></i>View Processed Text
                                            </button>
                                        </div>
                                        <div class="collapse" id="extractedTextCollapse">
                                            <div class="card card-body bg-light">
                                                <pre id="extractedTextContent" style="max-height: 400px; overflow-y: auto; white-space: pre-wrap; font-size: 12px;"></pre>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Hidden field for extracted text -->
                        <input type="hidden" name="jd_texts_extracted" id="hiddenExtractedText">
                    </div>
                </div>
            </div>
            <div class="card-footer bg-white text-end">
                <a href="<?= base_url('positions/view_positions/' . esc($position['position_group_id'])) ?>" class="btn btn-secondary">Cancel</a>
                <button type="submit" class="btn btn-primary">Update Position</button>
            </div>
        </form>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('styles') ?>
<style>
/* File Information Styles */
.info-item {
    margin-bottom: 8px;
}

.info-label {
    font-weight: 600;
    color: #495057;
    font-size: 14px;
}

.info-value {
    color: #212529;
    font-size: 14px;
    margin-left: 8px;
}

.progress-section {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 5px;
    padding: 15px;
}

.timing-info {
    font-size: 14px;
}

.extracted-text-section {
    border-top: 1px solid #e9ecef;
    padding-top: 15px;
}

.spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
    margin-left: 10px;
    vertical-align: middle;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}
</style>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js"></script>
<script>
// Gemini AI Configuration
const GEMINI_API_KEY = 'AIzaSyApNBtEwylsW_q5zUOvIDP3pj87WDShSLA';
const GEMINI_MODEL = 'gemini-2.5-flash-lite-preview-06-17';
const GEMINI_API_URL = `https://generativelanguage.googleapis.com/v1beta/models/${GEMINI_MODEL}:generateContent?key=${GEMINI_API_KEY}`;

let selectedFile = null;
let pdfDocument = null;
let totalPages = 0;
let extractedText = '';
let rawTextFromPDF = '';

// Set up PDF.js worker
pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js';

$(document).ready(function() {
    // File input handling
    document.getElementById('jdFileInput').addEventListener('change', handleFileSelect);
});

function handleFileSelect(e) {
    const file = e.target.files[0];
    if (file && file.type === 'application/pdf') {
        handleFile(file);
    } else if (file) {
        alert('Please select a valid PDF file.');
        e.target.value = ''; // Clear the input
    }
}

async function handleFile(file) {
    selectedFile = file;

    // Check file size (25MB = 25 * 1024 * 1024 bytes)
    const maxSize = 25 * 1024 * 1024;
    if (file.size > maxSize) {
        alert('File size must be less than 25MB');
        document.getElementById('jdFileInput').value = '';
        return;
    }

    // Show AI processing section
    document.getElementById('aiProcessingSection').style.display = 'block';
    document.getElementById('fileInfo').style.display = 'block';

    try {
        // Load PDF to get page count
        const arrayBuffer = await file.arrayBuffer();
        pdfDocument = await pdfjsLib.getDocument(arrayBuffer).promise;
        totalPages = pdfDocument.numPages;

        // Update file information
        document.getElementById('fileName').textContent = file.name;
        document.getElementById('fileSize').textContent = formatFileSize(file.size);
        document.getElementById('totalPages').textContent = totalPages;
        document.getElementById('processingStatus').textContent = 'Starting AI processing...';

        // Show warning for large documents
        if (totalPages > 20) {
            if (!confirm(`This document has ${totalPages} pages and may take 5+ minutes to process. Consider splitting into smaller files for faster processing. Continue?`)) {
                document.getElementById('jdFileInput').value = '';
                document.getElementById('aiProcessingSection').style.display = 'none';
                return;
            }
        }

        // First extract raw text directly from PDF
        await extractRawTextFromPDF();

        // Then start AI text extraction for display
        await processFileWithAI();

    } catch (error) {
        showError('Error loading PDF: ' + error.message);
        document.getElementById('jdFileInput').value = '';
        document.getElementById('aiProcessingSection').style.display = 'none';
    }
}

async function extractRawTextFromPDF() {
    if (!pdfDocument) {
        console.error('PDF document not loaded');
        return;
    }

    try {
        document.getElementById('processingStatus').textContent = 'Extracting raw text from PDF...';

        let allRawText = '';

        // Extract text from each page using PDF.js
        for (let pageNum = 1; pageNum <= totalPages; pageNum++) {
            const page = await pdfDocument.getPage(pageNum);
            const textContent = await page.getTextContent();

            // Combine all text items from the page
            let pageText = '';
            textContent.items.forEach(item => {
                pageText += item.str + ' ';
            });

            // Add page break indicator
            allRawText += `\n--- Page ${pageNum} ---\n${pageText}\n`;
        }

        rawTextFromPDF = allRawText.trim();
        console.log('Raw text extracted from PDF:', rawTextFromPDF.substring(0, 500) + '...');

        document.getElementById('processingStatus').textContent = 'Raw text extraction complete';

    } catch (error) {
        console.error('Error extracting raw text from PDF:', error);
        document.getElementById('processingStatus').textContent = 'Raw text extraction failed';
        showError('Failed to extract raw text from PDF: ' + error.message);
    }
}

async function processFileWithAI() {
    if (!selectedFile || !pdfDocument) {
        return;
    }

    // Show progress section
    document.getElementById('progressSection').style.display = 'block';
    document.getElementById('processingStatus').textContent = 'Processing with AI...';
    document.getElementById('statusText').textContent = 'Converting PDF to AI readable format...';
    document.getElementById('currentStatus').textContent = 'Starting conversion...';

    try {
        extractedText = '';

        // Process in chunks of 5 pages
        const pagesPerChunk = 5;
        const totalChunks = Math.ceil(totalPages / pagesPerChunk);

        for (let chunkIndex = 0; chunkIndex < totalChunks; chunkIndex++) {
            const startPage = chunkIndex * pagesPerChunk + 1;
            const endPage = Math.min((chunkIndex + 1) * pagesPerChunk, totalPages);

            // Update progress
            const progress = (chunkIndex / totalChunks) * 100;
            document.getElementById('progressFill').style.width = progress + '%';
            document.getElementById('statusText').textContent = `Processing pages ${startPage}-${endPage} (${chunkIndex + 1}/${totalChunks})`;
            document.getElementById('currentStatus').textContent = `Converting pages ${startPage}-${endPage} to images...`;

            // Convert pages to images
            const pageImages = await convertPagesToImages(startPage, endPage);

            // Extract text with Gemini AI
            document.getElementById('currentStatus').textContent = `AI analyzing pages ${startPage}-${endPage}...`;
            const chunkText = await extractTextWithGemini(pageImages, startPage, endPage);

            if (chunkText) {
                extractedText += chunkText;
            }

            // Small delay to prevent rate limiting
            await new Promise(resolve => setTimeout(resolve, 1000));
        }

        // Complete processing
        document.getElementById('progressFill').style.width = '100%';
        document.getElementById('statusText').textContent = 'AI processing completed successfully!';
        document.getElementById('currentStatus').textContent = 'Processing complete';
        document.getElementById('processingStatus').textContent = 'Completed';

        // Set the extracted text in hidden field
        document.getElementById('hiddenExtractedText').value = extractedText;

        // Display extracted text
        displayExtractedText();

        // Analyze and populate form fields
        await analyzeAndPopulateFields();

        showSuccess(`AI text extraction completed successfully! Processed ${totalPages} pages.`);

    } catch (error) {
        showError('Error processing PDF with AI: ' + error.message);
        document.getElementById('processingStatus').textContent = 'Failed';
        document.getElementById('progressSection').style.display = 'none';
    }
}

async function convertPagesToImages(startPage, endPage) {
    const images = [];

    for (let pageNum = startPage; pageNum <= endPage; pageNum++) {
        try {
            const page = await pdfDocument.getPage(pageNum);
            const scale = 2.0; // High resolution for better text recognition
            const viewport = page.getViewport({ scale });

            // Create canvas for image conversion
            const canvas = document.createElement('canvas');
            const context = canvas.getContext('2d');
            canvas.height = viewport.height;
            canvas.width = viewport.width;

            // Render PDF page to canvas
            await page.render({
                canvasContext: context,
                viewport: viewport
            }).promise;

            // Convert to base64 PNG image
            const imageData = canvas.toDataURL('image/png').split(',')[1];
            images.push({
                pageNumber: pageNum,
                data: imageData
            });

        } catch (error) {
            console.error(`Error converting page ${pageNum}:`, error);
        }
    }

    return images;
}

async function extractTextWithGemini(pageImages, startPage, endPage) {
    try {
        // Prepare the parts for Gemini API
        const parts = [
            {
                text: `Read and analyse each page thoroughly from pages ${startPage}-${endPage}. Extract all text content and provide a comprehensive analysis in JSON format.

                IMPORTANT: You must return ONLY valid JSON format. Do not include any markdown, headers, or other text outside the JSON.

                For each page, provide:
                1. A detailed comprehensive profile of the page content
                2. The complete original text from that page

                Return ONLY this JSON structure (no other text):

                {
                  "pages": [
                    {
                      "page_number": ${startPage},
                      "profile": {
                        "content_type": "description of what type of content this page contains",
                        "main_sections": ["list of main sections found on this page"],
                        "document_elements": ["headers", "tables", "forms", "lists", "etc"],
                        "key_information": "summary of the most important information on this page",
                        "layout_description": "description of how the content is organized on the page"
                      },
                      "original_text": "complete text content from this page, preserving structure and formatting"
                    }
                  ]
                }

                Process all ${pageImages.length} pages completely. Write everything in JSON format.`
            }
        ];

        // Add each page image
        pageImages.forEach(image => {
            parts.push({
                inline_data: {
                    mime_type: "image/png",
                    data: image.data
                }
            });
        });

        const requestBody = {
            contents: [{
                parts: parts
            }],
            generationConfig: {
                temperature: 0.1,
                maxOutputTokens: 64000,
                topP: 0.9,
                topK: 40,
                candidateCount: 1
            }
        };

        const response = await fetch(GEMINI_API_URL, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(requestBody)
        });

        if (!response.ok) {
            throw new Error(`Gemini API error: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();

        if (data.candidates && data.candidates.length > 0 && data.candidates[0] && data.candidates[0].content && data.candidates[0].content.parts && data.candidates[0].content.parts.length > 0) {
            let extractedText = data.candidates[0].content.parts[0].text;

            // Try to extract JSON from the response if it contains extra text
            try {
                const jsonMatch = extractedText.match(/\{[\s\S]*\}/);
                if (jsonMatch) {
                    const jsonText = jsonMatch[0];
                    // Validate it's proper JSON
                    JSON.parse(jsonText);
                    extractedText = jsonText;
                }
            } catch (jsonError) {
                console.warn('Response is not valid JSON, using as-is:', jsonError);
            }

            return extractedText;
        } else {
            console.error('Invalid API response structure:', data);
            throw new Error('No text content returned from Gemini API');
        }

    } catch (error) {
        console.error(`Error processing pages ${startPage}-${endPage}:`, error);
        return `{"error": "Error processing pages ${startPage}-${endPage}: ${error.message}"}`;
    }
}

async function analyzeAndPopulateFields() {
    if (!rawTextFromPDF) {
        console.warn('No raw text available for analysis');
        return;
    }

    try {
        // Show processing indicator
        document.getElementById('processingStatus').textContent = 'Analyzing content and populating fields...';

        // Prepare the prompt for direct text extraction from raw PDF content
        const analysisPrompt = `Extract the exact texts directly from the following raw job description content and organize them into JSON format. This is the original, unprocessed text from the PDF file. Do not analyze, summarize, or rewrite the content - extract the exact original texts as they appear in the document.

Look for sections related to:
1. **Qualifications**: Find exact text sections about educational requirements, certifications, degrees, professional qualifications
2. **Knowledge**: Find exact text sections about required knowledge areas, technical knowledge, domain expertise
3. **Skills & Competencies**: Find exact text sections about technical skills, soft skills, competencies, abilities
4. **Job Experiences**: Find exact text sections about work experience requirements, years of experience, specific experience types

Raw content from PDF file:
${rawTextFromPDF}

Provide the response in JSON format with dynamic structure based on what you find. Extract the exact original texts without any modifications. Use a structure like this but adapt it based on the actual content found:
{
  "qualifications": "exact text as found in document",
  "knowledge": "exact text as found in document",
  "skills_competencies": "exact text as found in document",
  "job_experiences": "exact text as found in document"
}

If a section is not found, return an empty string for that field. Extract the complete exact texts, not summaries.`;

        // Call Gemini AI for analysis
        const response = await fetch(GEMINI_API_URL, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                contents: [{
                    parts: [{
                        text: analysisPrompt
                    }]
                }],
                generationConfig: {
                    temperature: 0.0,
                    topK: 1,
                    topP: 0.1,
                    maxOutputTokens: 4096,
                }
            })
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();

        if (data.candidates && data.candidates.length > 0 && data.candidates[0] && data.candidates[0].content && data.candidates[0].content.parts && data.candidates[0].content.parts.length > 0) {
            let analysisResult = data.candidates[0].content.parts[0].text;

            // Try to extract JSON from the response
            try {
                const jsonMatch = analysisResult.match(/\{[\s\S]*\}/);
                if (jsonMatch) {
                    const jsonText = jsonMatch[0];
                    const fieldData = JSON.parse(jsonText);

                    // Populate the form fields
                    if (fieldData.qualifications) {
                        document.querySelector('textarea[name="qualifications"]').value = fieldData.qualifications.trim();
                    }
                    if (fieldData.knowledge) {
                        document.querySelector('textarea[name="knowledge"]').value = fieldData.knowledge.trim();
                    }
                    if (fieldData.skills_competencies) {
                        document.querySelector('textarea[name="skills_competencies"]').value = fieldData.skills_competencies.trim();
                    }
                    if (fieldData.job_experiences) {
                        document.querySelector('textarea[name="job_experiences"]').value = fieldData.job_experiences.trim();
                    }

                    // Update processing status
                    document.getElementById('processingStatus').textContent = 'Content analysis complete - Form fields populated!';

                    // Show success message
                    showSuccess('Form fields have been automatically populated based on the job description content!');

                } else {
                    throw new Error('No valid JSON found in analysis response');
                }
            } catch (jsonError) {
                console.error('Failed to parse analysis JSON:', jsonError);
                document.getElementById('processingStatus').textContent = 'Content analysis completed but failed to populate fields';
                showError('Content analysis completed but failed to populate form fields. Please populate manually.');
            }
        } else {
            throw new Error('Invalid analysis response from AI');
        }

    } catch (error) {
        console.error('Error analyzing content:', error);
        document.getElementById('processingStatus').textContent = 'Content analysis failed';
        showError('Failed to analyze content for field population: ' + error.message);
    }
}

function displayExtractedText() {
    try {
        // Try to parse and format JSON
        const jsonData = JSON.parse(extractedText);
        let formattedDisplay = '';

        if (jsonData.pages && Array.isArray(jsonData.pages)) {
            jsonData.pages.forEach(page => {
                formattedDisplay += `<div class="page-section mb-4">`;
                formattedDisplay += `<h5 class="text-primary">Page ${page.page_number}</h5>`;

                if (page.profile) {
                    formattedDisplay += `<div class="profile-section mb-3">`;
                    formattedDisplay += `<h6 class="text-secondary">Page Profile:</h6>`;
                    formattedDisplay += `<ul class="list-unstyled ms-3">`;
                    if (page.profile.content_type) formattedDisplay += `<li><strong>Content Type:</strong> ${page.profile.content_type}</li>`;
                    if (page.profile.key_information) formattedDisplay += `<li><strong>Key Information:</strong> ${page.profile.key_information}</li>`;
                    if (page.profile.main_sections) formattedDisplay += `<li><strong>Main Sections:</strong> ${page.profile.main_sections.join(', ')}</li>`;
                    if (page.profile.document_elements) formattedDisplay += `<li><strong>Elements:</strong> ${page.profile.document_elements.join(', ')}</li>`;
                    if (page.profile.layout_description) formattedDisplay += `<li><strong>Layout:</strong> ${page.profile.layout_description}</li>`;
                    formattedDisplay += `</ul></div>`;
                }

                if (page.original_text) {
                    formattedDisplay += `<div class="original-text-section">`;
                    formattedDisplay += `<h6 class="text-secondary">Original Text:</h6>`;
                    formattedDisplay += `<div class="border p-3 bg-light" style="white-space: pre-wrap; font-family: monospace; font-size: 0.9em;">${page.original_text}</div>`;
                    formattedDisplay += `</div>`;
                }

                formattedDisplay += `</div><hr>`;
            });
        } else {
            // Fallback for non-JSON format
            formattedDisplay = `<div class="border p-3 bg-light" style="white-space: pre-wrap;">${extractedText}</div>`;
        }

        document.getElementById('extractedTextContent').innerHTML = formattedDisplay;
    } catch (error) {
        // If JSON parsing fails, display as plain text
        console.warn('Failed to parse JSON, displaying as plain text:', error);
        document.getElementById('extractedTextContent').innerHTML = `<div class="border p-3 bg-light" style="white-space: pre-wrap;">${extractedText}</div>`;
    }

    document.getElementById('extractedTextSection').style.display = 'block';
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function showSuccess(message) {
    // You can implement a toast notification or alert here
    console.log('Success:', message);
}

function showError(message) {
    // You can implement a toast notification or alert here
    console.error('Error:', message);
    alert('Error: ' + message);
}
</script>
<?= $this->endSection() ?>